{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### import"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['/home/<USER>/projects/Marabou/maraboupy/examples', '/home/<USER>/miniconda3/envs/marabou/lib/python37.zip', '/home/<USER>/miniconda3/envs/marabou/lib/python3.7', '/home/<USER>/miniconda3/envs/marabou/lib/python3.7/lib-dynload', '', '/home/<USER>/.local/lib/python3.7/site-packages', '/home/<USER>/miniconda3/envs/marabou/lib/python3.7/site-packages', '/home/<USER>/miniconda3/envs/marabou/lib/python3.7/site-packages/IPython/extensions', '/home/<USER>/.ipython', '../..']\n", "sys.version_info(major=3, minor=7, micro=1, releaselevel='final', serial=0)\n"]}, {"data": {"text/plain": ["(None, None)"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import sys\n", "import warnings\n", "import numpy as np\n", "\n", "# Path to Marabou folder if you did not export it\n", "# sys.path.append('/home/<USER>/git/marabou')\n", "# print(sys.path), print(sys.version_info)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from maraboupy import Marabou"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### file names"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["nnet_file_name = \"../../src/input_parsers/acas_example/ACASXU_run2a_1_1_tiny_2.nnet\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### load the network from NNet file"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"scrolled": true}, "outputs": [], "source": ["net1 = Marabou.read_nnet(nnet_file_name)\n", "net1.setLowerBound(net1.outputVars[0][0], .5)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SAT\n", "input 0 = -0.32842287715105956\n", "input 1 = 0.18470882292589386\n", "input 2 = -0.015568802288230335\n", "input 3 = -0.02962162304431794\n", "input 4 = -0.12540689418385062\n", "output 0 = 0.5\n", "output 1 = -0.14025877552260937\n", "output 2 = 0.8484051939900278\n", "output 3 = -2.0968368845973573\n", "output 4 = 0.05156232928503657\n"]}], "source": ["vals1, stats1 = net1.solve()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Example Statistics"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["125"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["stats1.getNumSplits()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["1691"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["stats1.getTotalTime()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Eval Example"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SAT\n", "input 0 = -0.32842287715105956\n", "input 1 = 0.18470882292589386\n", "input 2 = -0.015568802288230335\n", "input 3 = -0.02962162304431794\n", "input 4 = -0.12540689418385062\n", "output 0 = 0.5\n", "output 1 = -0.14025877552260937\n", "output 2 = 0.8484051939900278\n", "output 3 = -2.0968368845973573\n", "output 4 = 0.05156232928503657\n"]}], "source": ["net2 = Marabou.read_nnet(nnet_file_name)\n", "net2.setLowerBound(net2.outputVars[0][0], .5)\n", "vals2, stats2 = net2.solve()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["1743"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["stats2.getTotalTime()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["input0 = -0.328422874212265\n", "input1 = 0.40932923555374146\n", "input2 = -0.017379289492964745\n", "input3 = -0.2747684121131897\n", "input4 = -0.30628132820129395\n", "\n", "output0 = 0.5\n", "output1 = -0.18876336514949799\n", "output2 = 0.8081545233726501\n", "output3 = -2.764213800430298\n", "output4 = -0.12992984056472778"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SAT\n", "input 0 = -0.328422874212265\n", "input 1 = 0.40932923555374146\n", "input 2 = -0.017379289492964745\n", "input 3 = -0.2747684121131897\n", "input 4 = -0.30628132820129395\n", "output 0 = 0.49999677515649005\n", "output 1 = -0.18876658957388143\n", "output 2 = 0.807785545376377\n", "output 3 = -2.7642226430352106\n", "output 4 = -0.12984317469803675\n"]}], "source": ["### Eval Example:\n", "net2 = Marabou.read_nnet(nnet_file_name)\n", "\n", "net2.setLowerBound(net2.inputVars[0][0], input0)\n", "net2.setUpperBound(net2.inputVars[0][0], input0)\n", "\n", "net2.setLowerBound(net2.inputVars[0][1], input1)\n", "net2.setUpperBound(net2.inputVars[0][1], input1)\n", "\n", "net2.setLowerBound(net2.inputVars[0][2], input2)\n", "net2.setUpperBound(net2.inputVars[0][2], input2)\n", "\n", "net2.setLowerBound(net2.inputVars[0][3], input3)\n", "net2.setUpperBound(net2.inputVars[0][3], input3)\n", "\n", "net2.setLowerBound(net2.inputVars[0][4], input4)\n", "net2.setUpperBound(net2.inputVars[0][4], input4)\n", "\n", "vals2, stats2 = net2.solve()\n", "\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DIFF0 = 3.2248435099524464e-06\n", "DIFF1 = 3.2244243834411712e-06\n", "DIFF2 = 0.0003689779962731121\n", "DIFF3 = 8.842604912739915e-06\n", "DIFF4 = -8.666586669103804e-05\n"]}], "source": ["print(\"DIFF0 = {}\".format(output0 - vals2[net2.outputVars[0][0]]))\n", "print(\"DIFF1 = {}\".format(output1 - vals2[net2.outputVars[0][1]]))\n", "print(\"DIFF2 = {}\".format(output2 - vals2[net2.outputVars[0][2]]))\n", "print(\"DIFF3 = {}\".format(output3 - vals2[net2.outputVars[0][3]]))\n", "print(\"DIFF4 = {}\".format(output4 - vals2[net2.outputVars[0][4]]))"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON><PERSON>", "language": "python", "name": "<PERSON><PERSON><PERSON>"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.1"}}, "nbformat": 4, "nbformat_minor": 2}