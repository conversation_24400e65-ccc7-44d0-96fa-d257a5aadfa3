{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### import"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['/usr/lib/python36.zip', '/usr/lib/python3.6', '/usr/lib/python3.6/lib-dynload', '', '/home/<USER>/.local/lib/python3.6/site-packages', '/usr/local/lib/python3.6/dist-packages', '/usr/lib/python3/dist-packages', '/home/<USER>/.local/lib/python3.6/site-packages/IPython/extensions', '/home/<USER>/.ipython', '/home/<USER>/git/azmarabou']\n"]}], "source": ["import sys\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "sys.path.append('/home/<USER>/git/azmarabou')\n", "print(sys.path)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"scrolled": true}, "outputs": [], "source": ["\n", "\n", "from maraboupy import DnCSolver\n", "from maraboupy import DnC\n", "import numpy as np\n", "from multiprocessing import Process, Pipe\n", "import os"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["#network_name = \"../../resources/nnet/acasxu/ACASXU_experimental_v2a_1_7\" # 1s\n", "#network_name = \"../../resources/nnet/acasxu/ACASXU_experimental_v2a_2_9\" # 2s\n", "#network_name = \".../../resources/nnet/acasxu/ACASXU_experimental_v2a_2_6\" # 10s\n", "#network_name = \"./acas/ACASXU_run2a_2_5_batch_2000\" # 30s\n", "#network_name = \"./acas/ACASXU_run2a_5_2_batch_2000\" # 45s\n", "#network_name = \"./acas/ACASXU_run2a_5_3_batch_2000\" # 60s\n", "network_name = \"../../resources/nnet/acasxu/ACASXU_experimental_v2a_5_1\" # 300s\n", "property_path = \"../../resources/properties/acas_property_3.txt\""]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# Arguments and initiate the solver\n", "num_workers = 3\n", "initial_splits = 0\n", "online_split = 2\n", "init_to = 5\n", "to_factor = 1.5\n", "splitting_strategy = 3\n", "\n", "solver = DnCSolver.DnCSolver(network_name, property_path, num_workers,\n", "                             initial_splits, online_split, init_to, to_factor,\n", "                             splitting_strategy)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# Initial split of the input region\n", "parent_conn, child_conn = Pipe()\n", "p = Process(target=DnC.getSubProblems, args=(solver, child_conn))\n", "p.start()\n", "sub_queries = parent_conn.recv()\n", "p.join()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING: Logging before flag parsing goes to stderr.\n", "W0710 11:23:25.186996 140103463110464 deprecation.py:323] From /home/<USER>/git/azmarabou/maraboupy/NumTasksDecider.py:133: FastGFile.__init__ (from tensorflow.python.platform.gfile) is deprecated and will be removed in a future version.\n", "Instructions for updating:\n", "Use tf.gfile.GFile.\n", "W0710 11:23:25.189408 140103463110464 deprecation_wrapper.py:119] From /home/<USER>/git/azmarabou/maraboupy/NumTasksDecider.py:134: The name tf.GraphDef is deprecated. Please use tf.compat.v1.GraphDef instead.\n", "\n", "Process Process-5:\n", "Traceback (most recent call last):\n", "  File \"/usr/lib/python3.6/multiprocessing/process.py\", line 258, in _bootstrap\n", "    self.run()\n", "  File \"/usr/lib/python3.6/multiprocessing/process.py\", line 93, in run\n", "    self._target(*self._args, **self._kwargs)\n", "  File \"/home/<USER>/git/azmarabou/maraboupy/DnCParallelSolver.py\", line 183, in worker\n", "    assert(num_created == len(subproblems))\n", "UnboundLocalError: local variable 'subproblems' referenced before assignment\n"]}], "source": ["# Solve the created subqueries\n", "solver.solve(sub_queries)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}