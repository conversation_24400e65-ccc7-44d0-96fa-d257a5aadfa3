<html>
<title>CxxTest</title>
<h1>Introduction</h1>

<p>CxxTest is a JUnit/CppUnit/xUnit-like framework for C++.

<p>Its advantages over existing alternatives are that it:
<ul>
<li>Doesn't require RTTI
<li>Doesn't require member template functions
<li>Doesn't require exception handling
<li>Doesn't require any external libraries (including memory management,
   file/console I/O, graphics libraries)
<li>Is distributed entirely as a set of header files
</ul>

<p>This makes it extremely portable and usable.

<p>CxxTest is available under the <a href="http://www.gnu.org/copyleft/lesser.html">GNU
Lesser General Public License</a>.

<p>See <a href="guide.html">the user's guide</a> for information.
It is also available as <a href="http://cxxtest.sourceforge.net/guide.pdf">a PDF file</a>.

<p>The version history is available <a href="http://cxxtest.sourceforge.net/Versions.html">here</a>.

<h1>Getting CxxTest</h1>
You can always get the latest release from 
<a href="http://sourceforge.net/project/showfiles.php?group_id=52834">here</a> or
<a href="http://dl.sourceforge.net/cxxtest">here</a>.

<p>There are several files you can download: 
<ul>
<li><code>cxxtest-<i>version</i>-1.noarch.rpm</code>
<li><code>cxxtest-<i>version</i>.tar.gz</code>
<li><code>cxxtest-<i>version</i>.zip</code>
<li><code>cxxtest-guide-<i>version</i>.pdf</code> (the user's guide)
</ul>
Note that, since CxxTest consists entirely of header files, 
there is no distinction between source and binary distribution.

<p>There are also files called <code>cxxtest-selftest-*</code>: these
are used (usually by me) to test the portability of CxxTest, so you
can probably do without them.

<p>If you just can't wait for the next release, I sometimes upload betas
to <a href="http://cxxtest.sourceforge.net/beta">here</a>.

<h1>Getting started</h1>
Get the sources and build the samples in the sample subdirectory.

<br>
<p>
<A href="http://sourceforge.net"> <IMG src="http://sourceforge.net/sflogo.php?group_id=52834&type=4" border="0" alt="SourceForge Logo"></A> 

</html>
