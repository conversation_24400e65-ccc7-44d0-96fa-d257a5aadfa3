# Microsoft Developer Studio Project File - Name="CxxTest_3_Generate" - Package Owner=<4>
# Microsoft Developer Studio Generated Build File, Format Version 6.00
# ** DO NOT EDIT **

# TARGTYPE "Win32 (x86) External Target" 0x0106

CFG=CxxTest_3_Generate - Win32 Debug
!MESSAGE This is not a valid makefile. To build this project using NMAKE,
!MESSAGE use the Export Makefile command and run
!MESSAGE 
!MESSAGE NMAKE /f "CxxTest_3_Generate.mak".
!MESSAGE 
!MESSAGE You can specify a configuration when running NMAKE
!MESSAGE by defining the macro CFG on the command line. For example:
!MESSAGE 
!MESSAGE NMAKE /f "CxxTest_3_Generate.mak" CFG="CxxTest_3_Generate - Win32 Debug"
!MESSAGE 
!MESSAGE Possible choices for configuration are:
!MESSAGE 
!MESSAGE "CxxTest_3_Generate - Win32 Release" (based on "Win32 (x86) External Target")
!MESSAGE "CxxTest_3_Generate - Win32 Debug" (based on "Win32 (x86) External Target")
!MESSAGE 

# Begin Project
# PROP AllowPerConfigDependencies 0
# PROP Scc_ProjName ""
# PROP Scc_LocalPath ""

!IF  "$(CFG)" == "CxxTest_3_Generate - Win32 Release"

# PROP BASE Use_MFC 0
# PROP BASE Use_Debug_Libraries 0
# PROP BASE Output_Dir "Release"
# PROP BASE Intermediate_Dir "Release"
# PROP BASE Cmd_Line "NMAKE /f CxxTest_3_Generate.mak"
# PROP BASE Rebuild_Opt "/a"
# PROP BASE Target_File "CxxTest_3_Generate.exe"
# PROP BASE Bsc_Name "CxxTest_3_Generate.bsc"
# PROP BASE Target_Dir ""
# PROP Use_MFC 0
# PROP Use_Debug_Libraries 0
# PROP Output_Dir "Release"
# PROP Intermediate_Dir "Release"
# PROP Cmd_Line "nmake runner.cpp"
# PROP Rebuild_Opt "/a"
# PROP Target_File "runner.cpp"
# PROP Bsc_Name ""
# PROP Target_Dir ""

!ELSEIF  "$(CFG)" == "CxxTest_3_Generate - Win32 Debug"

# PROP BASE Use_MFC 0
# PROP BASE Use_Debug_Libraries 1
# PROP BASE Output_Dir "Debug"
# PROP BASE Intermediate_Dir "Debug"
# PROP BASE Cmd_Line "NMAKE /f CxxTest_3_Generate.mak"
# PROP BASE Rebuild_Opt "/a"
# PROP BASE Target_File "CxxTest_3_Generate.exe"
# PROP BASE Bsc_Name "CxxTest_3_Generate.bsc"
# PROP BASE Target_Dir ""
# PROP Use_MFC 0
# PROP Use_Debug_Libraries 1
# PROP Output_Dir "Debug"
# PROP Intermediate_Dir "Debug"
# PROP Cmd_Line "nmake runner.cpp"
# PROP Rebuild_Opt "/a"
# PROP Target_File "runner.cpp"
# PROP Bsc_Name ""
# PROP Target_Dir ""

!ENDIF 

# Begin Target

# Name "CxxTest_3_Generate - Win32 Release"
# Name "CxxTest_3_Generate - Win32 Debug"

!IF  "$(CFG)" == "CxxTest_3_Generate - Win32 Release"

!ELSEIF  "$(CFG)" == "CxxTest_3_Generate - Win32 Debug"

!ENDIF 

# Begin Source File

SOURCE=.\Makefile
# End Source File
# Begin Source File

SOURCE=.\ReadMe.txt
# End Source File
# End Target
# End Project
