# How RnnVerify Calculates the Inductive Case

## Variable Indexing Scheme

RnnVerify uses a specific 4-variable pattern for each RNN cell at each time step:

```python
def add_rnn_cell(query, input_weights, hidden_weight, num_iterations, bias=0, print_debug=False):
    '''
    Create rnn cell --> add 4 parameters to the query and the equations that describe the cell
    The added parameters are (same order): i, s_i-1 f, s_i b, s_i f
    '''
    last_idx = query.getNumberOfVariables()
    query.setNumberOfVariables(last_idx + 4)  # i, s_i-1 f, s_i b, s_i f
```

**For each RNN cell, 4 consecutive variables are allocated:**
- **`i`** (index 0): Loop counter/time step variable
- **`s_i-1_f`** (index +1): Hidden state from **previous** time step (after ReLU)
- **`s_i_b`** (index +2): Hidden state at **current** time step (before ReLU) 
- **`s_i_f`** (index +3): Hidden state at **current** time step (after ReLU)

## Mathematical Formulation of Induction

The induction proof works as follows:

### 1. Base Case (i = 0)

```python
# make sure i == 0 (for induction base)
loop_equations = []
for i in loop_indices:
    loop_eq = MarabouCore.Equation()
    loop_eq.addAddend(1, i)
    loop_eq.setScalar(0)
    loop_equations.append(loop_eq)

# s_i-1 f == 0 (initial hidden state is zero)
zero_rnn_hidden = []
for idx in rnn_input_indices:
    base_hypothesis = MarabouCore.Equation()
    base_hypothesis.addAddend(1, idx)
    base_hypothesis.setScalar(0)
    zero_rnn_hidden.append(base_hypothesis)
```

**Base case constraints:**
- **`i = 0`**: We're at the first time step
- **`s_{-1}_f = 0`**: Initial hidden state is zero
- **Prove**: The invariant holds at time step 0

### 2. Inductive Hypothesis Creation

```python
def create_induction_hypothesis_from_invariant_eq():
    '''
    for example our invariant is that s_i f <= i, the induction hypothesis will be s_i-1 f <= i-1
    :return: the induction hypothesis
    '''
    scalar_diff = 0
    hypothesis_eq = []
    
    cur_temp_eq = MarabouCore.Equation(invariant_eq.getType())
    for addend in invariant_eq.getAddends():
        # if for example we have s_i f - 2*i <= 0 we want s_i-1 f - 2*(i-1) <= 0 <--> s_i-1 f -2i <= -2
        if addend.getVariable() in loop_indices:
            scalar_diff = addend.getCoefficient()
        # here we change s_i f to s_i-1 f
        if addend.getVariable() in rnn_output_indices:
            cur_temp_eq.addAddend(addend.getCoefficient(), addend.getVariable() - 2)
        else:
            cur_temp_eq.addAddend(addend.getCoefficient(), addend.getVariable())
    cur_temp_eq.setScalar(invariant_eq.getScalar() + scalar_diff)
```

**This transforms the invariant from time step `i` to time step `i-1`:**
- If invariant is: `s_i_f ≤ α·i + β`
- Hypothesis becomes: `s_{i-1}_f ≤ α·(i-1) + β = α·i + β - α`
- **Variable shift**: `s_i_f` (index) → `s_{i-1}_f` (index - 2)

### 3. Inductive Step (i ≥ 1)

```python
step_loop_eq = MarabouCore.Equation(MarabouCore.Equation.GE)
step_loop_eq.addAddend(1, loop_indices[0])
step_loop_eq.setScalar(1)

induction_hypothesis = create_induction_hypothesis_from_invariant_eq() + [step_loop_eq]
induction_step_equations = [induction_step]
```

**Inductive step constraints:**
- **`i ≥ 1`**: We're at time step 1 or later
- **Assume**: Invariant holds at time step `i-1` (induction hypothesis)
- **Prove**: Invariant holds at time step `i`

## How the RNN State Transition Works

The key insight is how RnnVerify models the RNN computation:

```python
# s_i b = input_weight * xi + hidden_weight * s(i-1)f
for k in range(1, n_iterations):
    cur_equation = MarabouCore.Equation()
    cur_equation.addAddend(input_weight, k)  # xk
    cur_equation.addAddend(hidden_weight, variables_first_index + (2 * k) - 1)  # s(k-1)f
    cur_equation.addAddend(-1, variables_first_index + (2 * k))  # skb
    cur_equation.setScalar(0)
    inputQuery.addEquation(cur_equation)

# ReLu's: s_i f = ReLU(s_i b)
for k in range(variables_first_index, variables_first_index + 2 * n_iterations, 2):
    MarabouCore.addReluConstraint(inputQuery, k, k + 1)
```

**RNN equations:**
1. **Linear transformation**: `s_i_b = W_input · x_i + W_hidden · s_{i-1}_f + bias`
2. **Activation**: `s_i_f = ReLU(s_i_b)`

## Complete Induction Process

### Step 1: Prove Base Case

```python
# first prove base case for all equations
for i, ls_eq in enumerate(base_eq):
    for eq in ls_eq:
        network.addEquation(eq)
    marabou_result, sat_vars = marabou_solve_negate_eq(network, print_vars=True, return_vars=True)
    
    for eq in ls_eq:
        network.removeEquation(eq)
    
    proved_invariants[i] = marabou_result
    if not marabou_result:
        print("induction base fail, on invariant {}".format(i))
```

**Query**: Can we find values that violate the invariant at time step 0?
- **UNSAT** → Base case proven ✓
- **SAT** → Base case failed ✗

### Step 2: Prove Inductive Step

```python
if not hypothesis_fail:
    for i, steq_eq_ls in enumerate(step_eq):
        for eq in steq_eq_ls:
            network.addEquation(eq)
        
        marabou_result, cur_vars = marabou_solve_negate_eq(network, print_vars=True, return_vars=True)
        
        if not marabou_result:
            proved_invariants[i] = False
        else:
            proved_invariants[i] = True
        
        for eq in steq_eq_ls:
            network.removeEquation(eq)
```

**Query**: Assuming the invariant holds at step `i-1`, can we find values that violate it at step `i`?
- **UNSAT** → Inductive step proven ✓
- **SAT** → Inductive step failed ✗

## Example: Concrete Invariant

Let's say we want to prove: **`s_i_f ≤ 2·i + 5`**

**Base Case (i=0):**
- Constraints: `i = 0`, `s_{-1}_f = 0`
- Query: `¬(s_0_f ≤ 2·0 + 5)` = `s_0_f > 5`
- If UNSAT → Base case holds

**Inductive Step (i≥1):**
- Assume: `s_{i-1}_f ≤ 2·(i-1) + 5 = 2·i + 3`
- Constraints: `i ≥ 1`, `s_{i-1}_f ≤ 2·i + 3`
- RNN equations: `s_i_b = W·x_i + H·s_{i-1}_f + b`, `s_i_f = ReLU(s_i_b)`
- Query: `¬(s_i_f ≤ 2·i + 5)` = `s_i_f > 2·i + 5`
- If UNSAT → Inductive step holds

## Why This Works

The brilliance of this approach is that it **avoids unrolling the entire RNN**. Instead of creating variables for all time steps 0 to N, it only needs:
1. **Two time steps**: `i-1` and `i`
2. **Mathematical induction**: Proves the property holds for **all** time steps
3. **Invariant search**: Automatically finds the right invariant parameters (α, β) using optimization

This makes verification **scalable** to long sequences while providing **formal guarantees** about the RNN's behavior across all possible time steps.

## Key Files Referenced

- `maraboupy/MarabouRNN.py` - Core induction equation creation
- `RNN/MarabouRNNMultiDim.py` - Multi-dimensional invariant proving
- `RNN/MarabouRnnModel.py` - RNN model encoding and variable bounds
- `maraboupy/examples/rnn_unfold.py` - Example of RNN state equations
