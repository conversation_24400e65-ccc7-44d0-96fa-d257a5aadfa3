/*********************                                                        */
/*! \file ConstraintMatrixAnalyzerFactory.cpp
 ** \verbatim
 ** Top contributors (to current version):
 **   Shantan<PERSON> Thakoor
 ** This file is part of the Marabou project.
 ** Copyright (c) 2017-2019 by the authors listed in the file AUTHORS
 ** in the top-level source directory) and their institutional affiliations.
 ** All rights reserved. See the file COPYING in the top-level source
 ** directory for licensing information.\endverbatim
 **
 ** [[ Add lengthier description here ]]

**/

#define CXXTEST_MOCK_TEST_SOURCE_FILE
#include "T/ConstraintMatrixAnalyzerFactory.h"

//
// Local Variables:
// compile-command: "make -C ../.. "
// tags-file-name: "../../TAGS"
// c-basic-offset: 4
// End:
//
