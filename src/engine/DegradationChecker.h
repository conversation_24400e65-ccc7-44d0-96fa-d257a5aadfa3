/*********************                                                        */
/*! \file DegradationChecker.h
 ** \verbatim
 ** Top contributors (to current version):
 **   <PERSON>, <PERSON>
 ** This file is part of the Marabou project.
 ** Copyright (c) 2017-2019 by the authors listed in the file AUTHORS
 ** in the top-level source directory) and their institutional affiliations.
 ** All rights reserved. See the file COPYING in the top-level source
 ** directory for licensing information.\endverbatim
 **
 ** [[ Add lengthier description here ]]

**/

#ifndef __DegradationChecker_h__
#define __DegradationChecker_h__

#include "List.h"

class Equation;
class ITableau;
class InputQuery;

class DegradationChecker
{
public:
    void storeEquations( const InputQuery &query );
    double computeDegradation( ITableau &tableau ) const;

private:
    List<Equation> _equations;

    double computeDegradation( const Equation &equation, ITableau &tableau ) const;
};

#endif // __DegradationChecker_h__

//
// Local Variables:
// compile-command: "make -C ../.. "
// tags-file-name: "../../TAGS"
// c-basic-offset: 4
// End:
//
