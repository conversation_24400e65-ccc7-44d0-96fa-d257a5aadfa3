Full network:
-------
node[1, 0] = 0.227630
node[1, 1] = 0.000000
node[1, 2] = 0.053409
node[1, 3] = 0.000000
node[1, 4] = 0.000000
node[1, 5] = 0.000000
node[1, 6] = 0.074607
node[1, 7] = 0.000000
node[1, 8] = 0.000000
node[1, 9] = 0.000000
node[1, 10] = 0.058711
node[1, 11] = 0.183917
node[1, 12] = 0.131957
node[1, 13] = 0.000000
node[1, 14] = 0.196929
node[1, 15] = 0.206664
node[1, 16] = 0.000000
node[1, 17] = 0.030658
node[1, 18] = 0.000000
node[1, 19] = 0.000000
node[1, 20] = 0.000000
node[1, 21] = 0.000000
node[1, 22] = 0.096963
node[1, 23] = 0.009833
node[1, 24] = 0.000000
node[1, 25] = 0.000000
node[1, 26] = 0.000000
node[1, 27] = 0.071015
node[1, 28] = 0.000000
node[1, 29] = 0.000000
node[1, 30] = 0.559084
node[1, 31] = 0.135483
node[1, 32] = 0.000000
node[1, 33] = 0.000000
node[1, 34] = 0.759638
node[1, 35] = 0.132825
node[1, 36] = 0.235566
node[1, 37] = 0.214858
node[1, 38] = 0.000000
node[1, 39] = 0.000000
node[1, 40] = 0.221606
node[1, 41] = 0.000000
node[1, 42] = 0.000000
node[1, 43] = 0.000000
node[1, 44] = 0.000000
node[1, 45] = 0.000000
node[1, 46] = 0.037034
node[1, 47] = 0.053809
node[1, 48] = 0.000000
node[1, 49] = 0.175917
node[2, 0] = 0.247606
node[2, 1] = 0.000000
node[2, 2] = 0.502216
node[2, 3] = 0.000000
node[2, 4] = 0.150101
node[2, 5] = 0.000000
node[2, 6] = 0.240814
node[2, 7] = 0.000000
node[2, 8] = 0.000000
node[2, 9] = 0.653363
node[2, 10] = 0.000000
node[2, 11] = 0.000000
node[2, 12] = 0.000000
node[2, 13] = 0.000000
node[2, 14] = 0.000000
node[2, 15] = 0.000000
node[2, 16] = 0.000000
node[2, 17] = 0.117646
node[2, 18] = 0.000000
node[2, 19] = 0.000000
node[2, 20] = 0.055412
node[2, 21] = 0.255019
node[2, 22] = 0.000000
node[2, 23] = 0.000000
node[2, 24] = 0.000000
node[2, 25] = 0.000000
node[2, 26] = 0.000000
node[2, 27] = 0.000000
node[2, 28] = 0.000000
node[2, 29] = 0.099375
node[2, 30] = 0.000000
node[2, 31] = 0.000000
node[2, 32] = 0.000000
node[2, 33] = 0.000000
node[2, 34] = 0.000000
node[2, 35] = 0.000000
node[2, 36] = 0.000000
node[2, 37] = 0.000000
node[2, 38] = 0.000000
node[2, 39] = 0.000000
node[2, 40] = 0.496471
node[2, 41] = 0.797273
node[2, 42] = 0.000000
node[2, 43] = 0.000000
node[2, 44] = 0.000000
node[2, 45] = 0.000000
node[2, 46] = 0.000000
node[2, 47] = 0.000000
node[2, 48] = 0.000000
node[2, 49] = 0.000000
node[3, 0] = 0.000000
node[3, 1] = 1.784544
node[3, 2] = 0.000000
node[3, 3] = 0.600965
node[3, 4] = 0.446340
node[3, 5] = 0.000000
node[3, 6] = 0.000000
node[3, 7] = 0.347537
node[3, 8] = 0.218693
node[3, 9] = 0.000000
node[3, 10] = 0.000000
node[3, 11] = 0.000000
node[3, 12] = 0.000000
node[3, 13] = 0.000000
node[3, 14] = 0.000000
node[3, 15] = 0.000000
node[3, 16] = 0.388790
node[3, 17] = 1.437268
node[3, 18] = 0.000000
node[3, 19] = 0.000000
node[3, 20] = 0.000000
node[3, 21] = 0.000000
node[3, 22] = 1.580966
node[3, 23] = 2.006824
node[3, 24] = 0.000000
node[3, 25] = 0.000000
node[3, 26] = 0.000000
node[3, 27] = 0.768900
node[3, 28] = 0.000000
node[3, 29] = 0.155510
node[3, 30] = 0.000000
node[3, 31] = 0.092394
node[3, 32] = 0.000000
node[3, 33] = 0.000000
node[3, 34] = 0.000000
node[3, 35] = 0.757368
node[3, 36] = 0.000000
node[3, 37] = 0.000000
node[3, 38] = 0.000000
node[3, 39] = 0.000000
node[3, 40] = 0.000000
node[3, 41] = 0.000000
node[3, 42] = 1.872185
node[3, 43] = 0.000000
node[3, 44] = 0.000000
node[3, 45] = 0.000000
node[3, 46] = 0.914132
node[3, 47] = 0.000000
node[3, 48] = 0.000000
node[3, 49] = 0.000000
node[4, 0] = 0.000000
node[4, 1] = 0.000000
node[4, 2] = 0.000000
node[4, 3] = 0.000000
node[4, 4] = 0.000000
node[4, 5] = 0.000000
node[4, 6] = 0.072753
node[4, 7] = 0.000000
node[4, 8] = 0.000000
node[4, 9] = 0.190321
node[4, 10] = 0.000000
node[4, 11] = 2.809642
node[4, 12] = 0.000000
node[4, 13] = 0.000000
node[4, 14] = 0.000000
node[4, 15] = 0.000000
node[4, 16] = 0.000000
node[4, 17] = 0.133378
node[4, 18] = 0.000000
node[4, 19] = 0.000000
node[4, 20] = 0.000000
node[4, 21] = 0.857072
node[4, 22] = 0.839832
node[4, 23] = 1.578740
node[4, 24] = 0.000000
node[4, 25] = 0.000000
node[4, 26] = 0.000000
node[4, 27] = 0.000000
node[4, 28] = 0.000000
node[4, 29] = 0.000000
node[4, 30] = 0.000000
node[4, 31] = 2.799295
node[4, 32] = 4.458405
node[4, 33] = 3.478529
node[4, 34] = 0.000000
node[4, 35] = 0.000000
node[4, 36] = 0.073468
node[4, 37] = 0.000000
node[4, 38] = 0.000000
node[4, 39] = 0.000000
node[4, 40] = 0.225169
node[4, 41] = 0.803132
node[4, 42] = 0.000000
node[4, 43] = 0.000000
node[4, 44] = 0.000000
node[4, 45] = 0.390207
node[4, 46] = 0.000000
node[4, 47] = 0.000000
node[4, 48] = 1.203420
node[4, 49] = 0.778815
node[5, 0] = 0.370712
node[5, 1] = 0.000000
node[5, 2] = 0.000000
node[5, 3] = 0.000000
node[5, 4] = 0.000000
node[5, 5] = 0.000000
node[5, 6] = 0.000000
node[5, 7] = 0.000000
node[5, 8] = 0.000000
node[5, 9] = 0.000000
node[5, 10] = 1.163259
node[5, 11] = 0.000000
node[5, 12] = 0.000000
node[5, 13] = 0.000000
node[5, 14] = 0.000000
node[5, 15] = 0.009856
node[5, 16] = 0.000000
node[5, 17] = 0.000000
node[5, 18] = 0.000000
node[5, 19] = 0.000000
node[5, 20] = 0.000000
node[5, 21] = 0.000000
node[5, 22] = 0.000000
node[5, 23] = 0.000000
node[5, 24] = 0.000000
node[5, 25] = 2.055022
node[5, 26] = 0.000000
node[5, 27] = 0.000000
node[5, 28] = 0.000000
node[5, 29] = 0.000000
node[5, 30] = 0.000000
node[5, 31] = 0.000000
node[5, 32] = 0.000000
node[5, 33] = 3.593772
node[5, 34] = 0.000000
node[5, 35] = 0.000000
node[5, 36] = 0.000000
node[5, 37] = 0.000000
node[5, 38] = 0.262936
node[5, 39] = 0.357490
node[5, 40] = 0.000000
node[5, 41] = 0.000000
node[5, 42] = 0.000000
node[5, 43] = 0.000000
node[5, 44] = 0.000000
node[5, 45] = 0.407692
node[5, 46] = 1.235331
node[5, 47] = 0.000000
node[5, 48] = 0.000000
node[5, 49] = 0.000000
node[6, 0] = 0.000000
node[6, 1] = 0.000000
node[6, 2] = 0.000000
node[6, 3] = 0.000000
node[6, 4] = 0.000000
node[6, 5] = 0.000000
node[6, 6] = 0.000000
node[6, 7] = 0.000000
node[6, 8] = 0.000000
node[6, 9] = 0.000000
node[6, 10] = 0.000000
node[6, 11] = 0.000000
node[6, 12] = 0.000000
node[6, 13] = 0.000000
node[6, 14] = 0.000000
node[6, 15] = 0.000000
node[6, 16] = 0.000000
node[6, 17] = 0.000000
node[6, 18] = 0.000000
node[6, 19] = 0.000000
node[6, 20] = 0.000000
node[6, 21] = 0.000000
node[6, 22] = 0.000000
node[6, 23] = 0.000000
node[6, 24] = 0.000000
node[6, 25] = 0.165445
node[6, 26] = 0.000000
node[6, 27] = 0.000000
node[6, 28] = 0.000000
node[6, 29] = 0.000000
node[6, 30] = 0.000000
node[6, 31] = 0.000000
node[6, 32] = 0.000000
node[6, 33] = 0.000000
node[6, 34] = 0.000000
node[6, 35] = 0.000000
node[6, 36] = 0.000000
node[6, 37] = 0.000000
node[6, 38] = 0.000000
node[6, 39] = 0.000000
node[6, 40] = 0.000000
node[6, 41] = 0.000000
node[6, 42] = 0.000000
node[6, 43] = 0.000000
node[6, 44] = 0.000000
node[6, 45] = 0.000000
node[6, 46] = 0.000000
node[6, 47] = 0.000000
node[6, 48] = 0.000000
node[6, 49] = 0.108896
node[7, 0] = -0.021199
node[7, 1] = -0.018714
node[7, 2] = -0.018766
node[7, 3] = -0.018762
node[7, 4] = -0.018760

	output[0] = -0.0211989
	output[1] = -0.0187142
	output[2] = -0.0187663
	output[3] = -0.0187621
	output[4] = -0.0187605


Tiny 1 (5,5,5)
---------------

node[0, 0] = 0.000000
node[0, 1] = 0.000000
node[0, 2] = 0.000000
node[0, 3] = 0.000000
node[0, 4] = 0.000000
node[1, 0] = 0.227630
node[1, 1] = 0.000000
node[1, 2] = 0.053409
node[1, 3] = 0.000000
node[1, 4] = 0.000000
node[2, 0] = 0.035470
node[2, 1] = -0.017883
node[2, 2] = 0.757338
node[2, 3] = -1.079073
node[2, 4] = 0.179470

Dumping outputs:
	output[0] = 0.0354704
	output[1] = -0.0178834
	output[2] = 0.757338
	output[3] = -1.07907
	output[4] = 0.17947

Tiny 2 (5,50,5)
---------------

node[0, 0] = 0.000000
node[0, 1] = 0.000000
node[0, 2] = 0.000000
node[0, 3] = 0.000000
node[0, 4] = 0.000000

node[1, 0] = 0.227630
node[1, 1] = 0.000000
node[1, 2] = 0.053409
node[1, 3] = 0.000000
node[1, 4] = 0.000000
node[1, 5] = 0.000000
node[1, 6] = 0.074607
node[1, 7] = 0.000000
node[1, 8] = 0.000000
node[1, 9] = 0.000000
node[1, 10] = 0.058711
node[1, 11] = 0.183917
node[1, 12] = 0.131957
node[1, 13] = 0.000000
node[1, 14] = 0.196929
node[1, 15] = 0.206664
node[1, 16] = 0.000000
node[1, 17] = 0.030658
node[1, 18] = 0.000000
node[1, 19] = 0.000000
node[1, 20] = 0.000000
node[1, 21] = 0.000000
node[1, 22] = 0.096963
node[1, 23] = 0.009833
node[1, 24] = 0.000000
node[1, 25] = 0.000000
node[1, 26] = 0.000000
node[1, 27] = 0.071015
node[1, 28] = 0.000000
node[1, 29] = 0.000000
node[1, 30] = 0.559084
node[1, 31] = 0.135483
node[1, 32] = 0.000000
node[1, 33] = 0.000000
node[1, 34] = 0.759638
node[1, 35] = 0.132825
node[1, 36] = 0.235566
node[1, 37] = 0.214858
node[1, 38] = 0.000000
node[1, 39] = 0.000000
node[1, 40] = 0.221606
node[1, 41] = 0.000000
node[1, 42] = 0.000000
node[1, 43] = 0.000000
node[1, 44] = 0.000000
node[1, 45] = 0.000000
node[1, 46] = 0.037034
node[1, 47] = 0.053809
node[1, 48] = 0.000000
node[1, 49] = 0.175917

node[2, 0] = 0.247606
node[2, 1] = -0.119279
node[2, 2] = 0.502216
node[2, 3] = -1.363399
node[2, 4] = 0.150101

Dumping outputs:
	output[0] = 0.247606
	output[1] = -0.119279
	output[2] = 0.502216
	output[3] = -1.3634
	output[4] = 0.150101
