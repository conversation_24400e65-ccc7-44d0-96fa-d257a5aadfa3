*.d
*.obj
TAGS
/src/engine/tests/Engine.tests
*~
/src/input_parsers/mps_generic/mps_gensol.elf
/src/input_parsers/berkeley_example/berkeley_example.elf
/src/basis_factorization/tests/basis_factorization.tests
/src/basis_factorization/tests/runner.cxx
/src/basis_factorization/tests/cxxtest_Test_LUFactorization.cxx
/src/basis_factorization/tests/cxxtest_Test_ForrestTomlinFactorization.cxx
/src/basis_factorization/tests/cxxtest_Test_PermutationMatrix.cxx
*.DS_Store
*-checkpoint.ipynb
*.so
*.pyc
/src/engine/marabou.elf
tools/boost_1_68_0
/tools/boost.unzipped
/tools/boost_1_68_0.tar.gz
/cpp_interface_example/example.elf
/src/input_parsers/mps_example/mps.elf
/.idea/*

/* CMAKE */
CMakeLists.txt.user
CMakeCache.txt
CMakeFiles
CMakeScripts
Testing
Makefile
cmake_install.cmake
install_manifest.txt
compile_commands.json
CTestTestfile.cmake
_deps
*.pdf
*.lyx
*.tar.gz
*.pkl
tools/pybind11-2.3.0
